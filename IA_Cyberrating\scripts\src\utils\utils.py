import configparser
import pandas as pd
import logging
import logging.handlers
from functools import wraps
import warnings
import smtplib
import os
import subprocess
import json
import inspect
from sqlalchemy import create_engine
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parents[2]
config_path = BASE_DIR / "config" / "ia.config"
# config_path = BASE_DIR / "config" / "ia_func_test.config"

def get_env_name() -> str:
    """
    Gets the hostname to deduce the environment platform

    Returns
    -------
    None : str
        The environment name
    """

    try:
        hostname = subprocess.check_output('hostname', shell=True).decode().strip()
    except subprocess.CalledProcessError as e:
        print(f"Erreur lors de l'exécution de la commande hostname: {e}")
    if hostname.startswith("dv"):
        return "DEV"
    elif hostname.startswith("ua"):
        return "PRE-PROD"
    elif hostname.startswith("op"):
        return "PROD"
    else:
        return ""


def get_version_ref():
    """
    Return the tag version and commit associated from .version file

    Returns
    -------
    None : str
        The tag name
    None : str
        The commit name
    """

    my_logger = logging.getLogger("CBR-VERSIONNER")
    version_file = os.path.join(BASE_DIR, ".version")
    try:
        with open(version_file) as json_file:
            data = json.load(json_file)
            return data["pipelines"][0]["ref"], data["pipelines"][0]["sha"][:8]
    except Exception as e:
        my_logger.error("Problem while reading {} : {}".format(version_file, e))
        return "", ""


def db_connexion(config_path=config_path):
    """
    Creates a MYSQL engine connection

    Returns
    -------
    engine : Engine
        a MYSQL engine connection
    """

    config = configparser.ConfigParser()
    config.read(config_path)

    user = config["DB"]["user"]
    pwd = config["DB"]["password"]
    host = config["DB"]["host"]
    port = config["DB"]["port"]
    db = config["DB"]["database"]

    # Connexion à la Base de données
    engine = create_engine("mysql+mysqldb://{}:{}@{}:{}/{}?charset=utf8mb4".format(user, pwd, host, port, db))

    return engine




def log_decorator(log_message: str):
    """
    Creates a function decorator with a custom message

    Parameters
    ----------
    log_message : str
        A custom log message for the decorator
    """
    def decorate(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            my_logger = logging.getLogger("CBR-STEP")
            my_logger.info(log_message)
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", category=FutureWarning)
                    res = func(*args, **kwargs)
                my_logger.info("Succeeded")
                return res
            except Exception as e:
                my_logger.error("Failed")
                my_logger.error(f"Exception raised in {func.__name__}. exception: {str(e)}")
                # Toujours s'assurer que la fonction retourne un DataFrame ou un tuple de DataFrames
                sig = inspect.signature(func)
                if sig.return_annotation == tuple:
                    return pd.DataFrame(), pd.DataFrame()
                elif sig.return_annotation == pd.DataFrame:
                    return pd.DataFrame()
                else:
                    return None
        return wrapper
    return decorate


def config_logger(config_path=config_path):
    """
    Configures a logger

    Returns
    -------
    my_logger : logger
        A personalize logger
    """

    config = configparser.ConfigParser()
    config.read(config_path)

    my_logger = logging.getLogger("CBR-CONFIG")
    handler = logging.handlers.SysLogHandler(address=(config["DB"]["log_ip"], int(config["DB"]["log_port"])))
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    handler.setFormatter(formatter)

    my_logger.setLevel(logging.DEBUG)
    my_logger.addHandler(handler)
    return my_logger


def attach_files_to_email(message, files):
    """
    Attache un ou plusieurs fichiers à un objet email MIME.

    Cette fonction prend un objet 'message' (de type MIMEMultipart)
    et une ou plusieurs chaînes de caractères représentant les noms de fichiers à attacher.
    Les fichiers sont supposés se trouver dans le dossier 'out' du répertoire de base `BASE_DIR`.

    Paramètres :
    ----------
    message : email.mime.multipart.MIMEMultipart
        L'objet email auquel les fichiers seront attachés.

    files : str ou list[str]
        Un nom de fichier (str) ou une liste de noms de fichiers à attacher au message.
        Les chemins sont relatifs à BASE_DIR/out/.
    """
    if isinstance(files, str):  # Si un seul fichier (chemin unique)
        files = [files]  # On le transforme en liste pour simplifier le traitement
    for filename in files:
        file_path = os.path.join(BASE_DIR, "out", filename)
        try:
            # Ouvre le fichier en mode binaire
            with open(file_path, "rb") as attachment:
                part = MIMEBase("application", "octet-stream")
                part.set_payload(attachment.read())

            # Encode le fichier en ASCII pour l'envoi par email
            encoders.encode_base64(part)
            # Ajoute l'en-tête pour la pièce jointe
            part.add_header(
                "Content-Disposition",
                f"attachment; filename= {os.path.basename(filename)}",
            )
            # Attache la pièce jointe au message
            message.attach(part)

        except FileNotFoundError:
            print(f"Le fichier {filename} n'a pas été trouvé.")


def send_csv_mail(csv_file: str, config_path=config_path) -> None:
    """
    Sends a mail with a particular csv in attachment

    Parameters
    ----------
    csv_file : str
        The name of the csv file to attach
    """

    config = configparser.ConfigParser()
    config.read(config_path)

    env_name = get_env_name()

    if csv_file == "data_today.csv":
        info = "Notes du jour"
    elif csv_file == "test":
        info = "Indicateurs de taux de corrections dans les temps"
        csv_file = ["on_time_correction_rate.csv"]
    else:
        info = "IP vulnérables du jour"

    subject = "[CYBERRATING - IA - " + env_name + "] " + info
    body = "Bonjour. Ci-joint les " + info + ", en sortie du moteur de notation interne."
    sender_email = "<EMAIL>"
    receiver_email = "<EMAIL>"

    smtp_host = config["DB"]["smtp_host"]
    smtp_port = int(config["DB"]["smtp_port"])

    # Create a multipart message and set headers
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = receiver_email
    message["Subject"] = subject
    message["Bcc"] = receiver_email  # Recommended for mass emails

    # Add body to email
    message.attach(MIMEText(body, "plain"))
    attach_files_to_email(message, csv_file)
    text = message.as_string()

    # Log in to server using secure context and send email
    smtp_server = smtplib.SMTP(smtp_host, smtp_port)
    # smtp_server.login(sender_email, password)
    smtp_server.sendmail(sender_email, receiver_email, text)

def check_sources(import_obj, logger, prefix=""):
    """
    Check that the DataFrames of DataImport are not empty and contain the expected columns.
    Log the detected anomalies, specifying the SQL source table.
    """
    # Required data dictionary : attribute name -> (expected columns, SQL table name)
    expected = {
        'tenable_plugin': (None, 'vulnerability_management_coralystenableplugin'),
        'ref_rt_asset': (['ip_id', 'asset'], 'inventory_ip_critical_assets / inventory_fqdn_assets'),
        'db_category': (['privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type', 'cluster', 'division', 'asset', 'group'], 'cyber_rating_orangecyberratinggradecategory'),
        'ref_severity': (None, 'cyber_rating_orangecyberratingseverity'),
        'asset': (None, 'inventory_criticalasset'),
        'subsidiary': (None, 'organization_subsidiary'),
        'cluster': (None, 'organization_cluster'),
        'division': (None, 'organization_division'),
        'pc_scan_result': (['scan_date', 'ip_id', 'plugin_id', 'subsidiary', 'cluster', 'division'], 'vulnerability_management_coralystenablepluginscan / vulnerability_management_coralystenablefqdnpluginscan'),
        'mapping': (['orange_cyber_rating_category_id', 'field_value'], 'cyber_rating_mappingsourcetoorangecyberrating'),
        'category': (['id', 'name', 'orange_cyber_rating_family_id'], 'cyber_rating_orangecyberratingcategory'),
        'family': (['id', 'name'], 'cyber_rating_orangecyberratingfamily'),
        'ref_rt': (['id', 'subsidiary', 'cluster', 'division'], 'inventory_ip / inventory_fqdn'),
        'rating_letters': (['letter', 'value'], 'cyber_rating_ratinglettersetting'),
        'table_pc_history': (['ip_id', 'division', 'cluster', 'subsidiary', 'asset', 'severity', 'correction'], 'cyber_rating_patchingcadencenew / cyber_rating_patchingcadencenewfqdn'),
        'table_data_family': (None, 'cyber_rating_orangecyberratinggradefamily'),
        
    }
    for attr, (cols, table) in expected.items():
        df = getattr(import_obj, attr, None)
        table_info = f" (table: {table})"
        if df is None:
            logger.error(f"{prefix}{attr}: Uninitialized data !{table_info}")
            continue
        if not isinstance(df, pd.DataFrame):
            logger.error(f"{prefix}{attr}: Not a DataFrame (type={type(df)}){table_info}")
            continue
        if df.empty:
            logger.warning(f"{prefix}{attr}: Empty DataFrame !{table_info}")
        if cols is not None:
            missing = set(cols) - set(df.columns)
            if missing:
                logger.warning(f"{prefix}{attr}: Missing required columns : {missing}{table_info}")

if __name__ == "__main__":
    pd.set_option("display.max_columns", 500)
    pd.set_option("display.max_rows", 500)

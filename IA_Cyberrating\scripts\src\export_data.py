import pandas as pd
import numpy as np
from datetime import date
from sqlalchemy import text
from .utils import db_connexion
import logging


class DataExport:
    """
    A class used to export all the datas

    Attributes
    ----------
    engine : Engine
        a MYSQL engine connection

    Methods
    -------
    delete_today()
        Deletes today data for rating tables if it exists
    rename_data_tables(category, family, total, findings, findings_assets, patching_cadence)
        Renames some columns for rating tables
    export_append_tables(df, table_name)
        Exports a rating table
    export_replace_tables(df, table_name)
        Exports a daily table
    rating_tables_export(category, family, total, findings, findings_assets, data_pc)
        Runs all functions to export rating tables
    rename_correction_tables
        Renames some columns for correction tables
    correction_tables_export
        Runs all functions to export correction tables
    """

    def __init__(self):
        self.engine = db_connexion()

    def delete_today(self) -> None:
        """
        Deletes today data for rating tables if it exists
        """

        today = date.today()
        tables_list = [
            "cyber_rating_orangecyberratinggradecategory",
            "cyber_rating_orangecyberratinggradefamily",
            "cyber_rating_orangecyberratinggrade"
        ]

        for table in tables_list:
            max_date = pd.read_sql("SELECT MAX(timestamp) FROM {}".format(table), self.engine)
            max_date = max_date.loc[0, "MAX(timestamp)"]

            if today == max_date:
                with self.engine.connect() as conn:
                    conn.execute(text("DELETE FROM {} WHERE timestamp = '{}'".format(table, today)))
                    conn.commit()

    @staticmethod
    def rename_data_tables(
            category: pd.DataFrame,
            family: pd.DataFrame,
            total: pd.DataFrame,
            findings: pd.DataFrame,
            findings_assets: pd.DataFrame,
            findings_fqdn: pd.DataFrame,
            findings_assets_fqdn: pd.DataFrame,
            pc_history: pd.DataFrame,
            pc_history_fqdn: pd.DataFrame,
            severity_pc_mean: pd.DataFrame,
            vulnerable_ips: pd.DataFrame) -> None:
        """
        Renames some columns for rating tables

        Parameters
        ----------
        category : DataFrame
            The category rating dataframe
        family: DataFrame
            The family rating dataframe
        total : DataFrame
            The total rating dataframe
        findings : DataFrame
            The dataframe with all scans for entities since 45 days about ip
        findings_assets : DataFrame
            The dataframe with all scans for assets since 45 days about ip
        findings_fqdn : DataFrame
            The dataframe with all scans for entities since 45 days about fqdn
        findings_assets_fqdn : DataFrame
            The dataframe with all scans for assets since 45 days about fqdn
        pc_history : DataFrame
            The dataframe with all scans and information about ip
        pc_history_fqdn : DataFrame
            The dataframe with all scans and information about fqdn
        severity_pc_mean : DataFrame
            The dataframe about mean correction delay by severity
        vulnerable_ips : DataFrame, optional
            The dataframe with vulnerable IPs data
        """

        category.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        family.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        total.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        findings.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id"},
            inplace=True)
        
        findings_fqdn.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id"},
            inplace=True)

        findings_assets.rename(columns={"asset": "asset_id"}, inplace=True)
        # # TODO : Add in DB
        # L'ignore errors permet de gérer les dataframes vides
        findings_assets.drop("group", axis=1, inplace=True, errors='ignore')
        findings_assets_fqdn.rename(columns={"asset": "asset_id"}, inplace=True)
        # # TODO : Add in DB
        findings_assets_fqdn.drop("group", axis=1, inplace=True, errors='ignore')

        pc_history.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "severity": "severity_id"},
            inplace=True)
        pc_history = pc_history.fillna('')

        pc_history_fqdn.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "severity": "severity_id"},
            inplace=True)
        pc_history_fqdn = pc_history_fqdn.fillna('')

        severity_pc_mean.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id"},
            inplace=True)
        severity_pc_mean = severity_pc_mean.fillna('')

        vulnerable_ips.rename(
                columns={
                    "IP_address": "ip",
                    "info": "nb_info",
                    "low": "nb_low", 
                    "medium": "nb_medium",
                    "high": "nb_high",
                    "critical": "nb_critical",
                    "timestamp": "created_at"
                },
                inplace=True)
        vulnerable_ips = vulnerable_ips.fillna('')
        
    def convert_names_to_ids(self, vulnerable_ips: pd.DataFrame) -> pd.DataFrame:
        """
        Converts division_name, cluster_name, subsidiary_name to their corresponding IDs
        by querying the reference tables from the database.

        Parameters
        ----------
        vulnerable_ips : DataFrame
            The vulnerable_ips dataframe with name columns

        Returns
        -------
        DataFrame
            The dataframe with ID columns for database export (names are kept for CSV)
        """
        if vulnerable_ips is None or vulnerable_ips.empty:
            return vulnerable_ips
            
        try:
            with self.engine.connect() as conn:
                subsidiary_ref = pd.read_sql("SELECT id, long_name FROM organization_subsidiary", conn)
                cluster_ref = pd.read_sql("SELECT id, name FROM organization_cluster", conn)
                division_ref = pd.read_sql("SELECT id, long_name FROM organization_division", conn)
                
                # Convert subsidiary_name to subsidiary_id
                vulnerable_ips = vulnerable_ips.merge(
                    subsidiary_ref, 
                    left_on='subsidiary_name', 
                    right_on='long_name', 
                    how='left'
                )
                vulnerable_ips['subsidiary_id'] = vulnerable_ips['id']
                vulnerable_ips.drop(['id', 'long_name'], axis=1, inplace=True)
                
                # Convert cluster_name to cluster_id
                vulnerable_ips = vulnerable_ips.merge(
                    cluster_ref, 
                    left_on='cluster_name', 
                    right_on='name', 
                    how='left'
                )
                vulnerable_ips['cluster_id'] = vulnerable_ips['id']
                vulnerable_ips.drop(['id', 'name'], axis=1, inplace=True)
                
                # Convert division_name to division_id
                vulnerable_ips = vulnerable_ips.merge(
                    division_ref, 
                    left_on='division_name', 
                    right_on='long_name', 
                    how='left'
                )
                vulnerable_ips['division_id'] = vulnerable_ips['id']
                vulnerable_ips.drop(['id', 'long_name'], axis=1, inplace=True)
                
                # Handle NaN values in cluster_id
                vulnerable_ips['cluster_id'] = vulnerable_ips['cluster_id'].where(
                    vulnerable_ips['cluster_id'].notna(), None
                )
                
                # Create a copy without name columns for database export (.csv is expecting names instead)
                vulnerable_ips_db = vulnerable_ips.copy()
                name_columns = ['division_name', 'cluster_name', 'subsidiary_name', 'ip_id']
                for col in name_columns:
                    if col in vulnerable_ips_db.columns:
                        vulnerable_ips_db.drop(col, axis=1, inplace=True)
                
                return vulnerable_ips_db
                
        except Exception as e:
            my_logger = logging.getLogger("CBR-EXPORT")
            my_logger.error(f"Error converting names to IDs: {str(e)}")
            raise e

    def export_append_tables(self, df: pd.DataFrame, table_name: str) -> None:
        """
        Exports a rating table

        Parameters
        ----------
        df : DataFrame
            The dataframe to export
        table_name: str
            The name of the table
        """

        df = df[~((df.cluster_id >= 1000) & (df.level == "cluster"))]
        df = df.copy()
        df["cluster_id"] = [np.nan if i > 999 else i for i in df.cluster_id]
        df.to_sql(table_name, self.engine, if_exists="append", index=False)

    def export_replace_tables(self, df, table_name):
        """
        Exports a daily table

        Parameters
        ----------
        df : DataFrame
            The dataframe to export
        table_name: str
            The name of the table
        """
        try:
            with self.engine.connect() as conn:
                # Clear existing data first
                conn.execute(text("DELETE FROM {}".format(table_name)))
                conn.commit()
                
                # Insert new data
                if not df.empty:
                    df.to_sql(table_name, con=conn, if_exists="append", index=False)
                    conn.commit()
                
        except Exception as e:
            print(f"Error exporting to {table_name}: {str(e)}")
            # Log the error but don't fail the entire process
            raise e
        finally:
            if 'conn' in locals():
                conn.close()

    def rating_tables_export(
            self,
            category: pd.DataFrame,
            family: pd.DataFrame,
            total: pd.DataFrame,
            findings: pd.DataFrame,
            findings_assets: pd.DataFrame,
            findings_fqdn: pd.DataFrame,
            findings_assets_fqdn: pd.DataFrame,
            pc_history: pd.DataFrame,
            pc_history_fqdn: pd.DataFrame,
            severity_pc_mean: pd.DataFrame,
            vulnerable_ips: pd.DataFrame,
            ) -> None:
        """
        Runs all functions to export rating tables

        Parameters
        ----------
        category : DataFrame
            The category rating dataframe
        family: DataFrame
            The family rating dataframe
        total : DataFrame
            The total rating dataframe
        findings : DataFrame
            The dataframe with all scans for entities since 45 days about ip
        findings_assets : DataFrame
            The dataframe with all scans for assets since 45 days about ip
        findings_fqdn : DataFrame
            The dataframe with all scans for entities since 45 days about fqdn
        findings_assets_fqdn : DataFrame
            The dataframe with all scans for assets since 45 days about fqdn
        pc_history : DataFrame
            The dataframe with all scans and information about ip
        pc_history_fqdn : DataFrame
            The dataframe with all scans and information about fqdn
        severity_pc_mean : DataFrame
            The dataframe about mean correction delay by severity
        vulnerable_ips : DataFrame
            The dataframe with vulnerable IPs data
        """
        my_logger = logging.getLogger("CBR-EXPORT")

        my_logger.info("Started rating tables export...")
        try:
            my_logger.info("Cleaning today's data...")
            self.delete_today()

            self.rename_data_tables(category, family, total, findings, findings_assets,
                                    findings_fqdn, findings_assets_fqdn, pc_history,
                                    pc_history_fqdn, severity_pc_mean, vulnerable_ips)

            my_logger.info(f"Appending : category ({len(category)}) -> cyber_rating_orangecyberratinggradecategory")
            self.export_append_tables(category, "cyber_rating_orangecyberratinggradecategory")
            my_logger.info(f"Appending : family ({len(family)}) -> cyber_rating_orangecyberratinggradefamily")
            self.export_append_tables(family, "cyber_rating_orangecyberratinggradefamily")
            my_logger.info(f"Appending : total ({len(total)}) -> cyber_rating_orangecyberratinggrade")
            self.export_append_tables(total, "cyber_rating_orangecyberratinggrade")

            if 'cluster_id' in findings.columns:
                findings["cluster_id"] = [np.nan if i > 999 else i for i in findings.cluster_id]
            else:
                findings["cluster_id"] = np.nan

            if 'cluster_id' in findings_fqdn.columns:
                findings_fqdn["cluster_id"] = [np.nan if i > 999 else i for i in findings_fqdn.cluster_id]
            else:
                findings_fqdn["cluster_id"] = np.nan

            pc_history = pc_history.copy()
            if 'cluster_id' in pc_history.columns:
                pc_history["cluster_id"] = [np.nan if i > 999 else i for i in pc_history.cluster_id]
            else:
                pc_history["cluster_id"] = np.nan
            
            pc_history_fqdn = pc_history_fqdn.copy()
            if 'cluster_id' in pc_history_fqdn.columns:
                pc_history_fqdn["cluster_id"] = [np.nan if i > 999 else i for i in pc_history_fqdn.cluster_id]
            else:
                pc_history_fqdn["cluster_id"] = np.nan

            severity_pc_mean = severity_pc_mean.copy()
            severity_pc_mean = severity_pc_mean[~((severity_pc_mean.cluster_id >= 1000) & (severity_pc_mean.level == "cluster"))]
            severity_pc_mean = severity_pc_mean.copy()
            severity_pc_mean["cluster_id"] = [np.nan if i > 999 else i for i in severity_pc_mean.cluster_id]

            my_logger.info(f"Export replace : findings ({len(findings)}) -> cyber_rating_datafindings")
            self.export_replace_tables(findings, "cyber_rating_datafindings")
            my_logger.info(f"Export replace : findings_assets ({len(findings_assets)}) -> cyber_rating_datafindingsasset")
            self.export_replace_tables(findings_assets, "cyber_rating_datafindingsasset")
            my_logger.info(f"Export replace : findings_fqdn ({len(findings_fqdn)}) -> cyber_rating_datafindingsfqdn")
            self.export_replace_tables(findings_fqdn, "cyber_rating_datafindingsfqdn")
            my_logger.info(f"Export replace : findings_assets_fqdn ({len(findings_assets_fqdn)}) -> cyber_rating_datafindingsassetfqdn")
            self.export_replace_tables(findings_assets_fqdn, "cyber_rating_datafindingsassetfqdn")
            my_logger.info(f"Export replace : pc_history ({len(pc_history)}) -> cyber_rating_patchingcadencenew")
            self.export_replace_tables(pc_history, "cyber_rating_patchingcadencenew")
            my_logger.info(f"Export replace : pc_history_fqdn ({len(pc_history_fqdn)}) -> cyber_rating_patchingcadencenewfqdn")
            self.export_replace_tables(pc_history_fqdn, "cyber_rating_patchingcadencenewfqdn")
            my_logger.info(f"Export replace : severity_pc_mean ({len(severity_pc_mean)}) -> cyber_rating_patchingcadenceseveritymean")
            self.export_replace_tables(severity_pc_mean, "cyber_rating_patchingcadenceseveritymean")
            vulnerable_ips_with_ids = self.convert_names_to_ids(vulnerable_ips)
            my_logger.info(f"Export replace : vulnerable_ips ({len(vulnerable_ips_with_ids)}) -> stats_statvulnerableip")
            self.export_replace_tables(vulnerable_ips_with_ids, "stats_statvulnerableip")

        except Exception as e:
            my_logger.error(f"Error during export : {str(e)}", exc_info=True)
            raise

    @staticmethod
    def rename_correction_tables(category: pd.DataFrame, family: pd.DataFrame,
                                 ip: pd.DataFrame, fqdn: pd.DataFrame) -> None:
        """
        Renames some columns for correction tables

        Parameters
        ----------
        category : DataFrame
            The dataframe with category correction
        family: DataFrame
            The dataframe with family correction
        ip : DataFrame
            The dataframe with ip correction
        ip : DataFrame
            The dataframe with fqdn correction
        """

        category.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        family.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        ip.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        fqdn.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

    def correction_tables_export(self, category: pd.DataFrame, family: pd.DataFrame,
                                 ip: pd.DataFrame, fqdn: pd.DataFrame) -> None:
        """
        Runs all functions to export correction tables

        Parameters
        ----------
        category : DataFrame
            The dataframe with category correction
        family: DataFrame
            The dataframe with family correction
        ip : DataFrame
            The dataframe with ip correction
        fqdn : DataFrame
            The dataframe with fqdn correction
        """

        self.rename_correction_tables(category, family, ip, fqdn)
        self.export_replace_tables(category, "cyber_rating_categorycorrection")
        self.export_replace_tables(family, "cyber_rating_familycorrection")
        self.export_replace_tables(ip, "cyber_rating_ipcorrection")
        self.export_replace_tables(fqdn, "cyber_rating_fqdncorrection")

    def table_write_test(self):
        table_list = ["cyber_rating_orangecyberratinggradecategory",
                      "cyber_rating_orangecyberratinggradefamily",
                      "cyber_rating_orangecyberratinggrade",
                      "cyber_rating_patchingcadencenew",
                      "cyber_rating_patchingcadencenewfqdn",
                      "stats_statvulnerableip",
                      "cyber_rating_categorycorrection",
                      "cyber_rating_familycorrection",
                      "cyber_rating_ipcorrection",
                      "cyber_rating_fqdncorrection"]

        for table_name in table_list:
            try:
                with self.engine.connect() as conn:
                    # Stats_statvulnerableip has a different timestamp column name (created_at)
                    if table_name == "stats_statvulnerableip":
                        result = conn.execute(text("SELECT MAX(created_at) AS max_timestamp FROM {}".format(table_name)))
                        max_date = result.scalar()
                        if max_date is not None:
                            max_date = pd.to_datetime(max_date).date()
                    else:
                        result = conn.execute(text("SELECT MAX(timestamp) AS max_timestamp FROM {}".format(table_name)))
                        max_date = result.scalar()  # Récupérer la valeur de max_timestamp

                if max_date == date.today():
                    continue
                else:
                    raise ValueError(f"The '{table_name}' table was not loaded today. Last loaded : {max_date}")
            except ValueError as e:
                # Affiche l'erreur mais continue le script
                print(f"Erreur: {e}")


if __name__ == "__main__":
    pd.set_option("display.max_columns", 500)
    pd.set_option("display.max_rows", 500)
